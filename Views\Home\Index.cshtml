@{
    ViewData["Title"] = "iTicket System";
}

<style>
    body {
        background-color: #f8f9fa;
    }
    .hero-section {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .hero-text {
        padding: 30px;
    }
    .hero-image {
        padding: 30px;
        text-align: center;
    }
    .btn-lg {
        min-width: 220px;
        padding: 14px 20px;
        font-size: 1.1rem;
    }
    .footer-text {
        margin-top: 40px;
        color: #888;
        font-size: 0.9rem;
    }
</style>

<div class="container hero-section">
    <div class="row align-items-center">
        <!-- LEFT SIDE -->
        <div class="col-md-6 hero-text text-center text-md-start">
            <h1 class="fw-bold mb-3">iTicket System</h1>
            <p class="lead mb-4">An easy, convenient and efficient IT issue reporting system</p>
            <h5 class="mb-3">Select the service you need</h5>
            <div class="d-grid gap-3 d-md-block">
                <a href="/Issue/Create" class="btn btn-success btn-lg me-2 mb-2">
                    <i class="bi bi-pencil-square me-2"></i> Create a New Ticket
                </a>
                <a href="/Issue/Track" class="btn btn-primary btn-lg mb-2">
                    <i class="bi bi-search me-2"></i> Check Ticket Status
                </a>
            </div>
            <div class="footer-text">
                © 2025 Namchow IT. All rights reserved.
            </div>
        </div>

        <!-- RIGHT SIDE IMAGE -->
        <div class="col-md-6 hero-image">
            <img src="/images/it-support-illustration-smile.png" class="img-fluid" alt="IT Support Illustration">
        </div>
    </div>
</div>

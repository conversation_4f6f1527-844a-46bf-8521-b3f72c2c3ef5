@{
    ViewData["Title"] = "iTicket System";
}

<style>
    body {
        background-color: #f8f9fa;
    }
    .card {
        border: none;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }
    .btn-lg {
        min-width: 200px;
    }
    footer {
        color: #888;
    }
</style>

<div class="container py-5">
    <!-- COMPANY LOGO -->
    <div class="text-center mb-4">
        <img src="/images/company-logo.png" alt="Company Logo" height="60">
    </div>

    <!-- TITLE -->
    <div class="text-center mb-5">
        <h1 class="display-4 fw-bold">iTicket System</h1>
        <p class="lead">An easy, convenient and efficient IT issue reporting system</p>
    </div>

    <!-- CARD -->
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card p-4 text-center bg-white">
                
                <!-- IMAGE ILLUSTRATION -->
                <img src="/images/it-support-illustration.png" class="img-fluid mb-3" alt="IT Support Illustration">
                
                <h3 class="mb-4">Select the service you need</h3>
                
                <div class="d-grid gap-3">
                    <a href="/Issue/Create" class="btn btn-success btn-lg">
                        <i class="bi bi-pencil-square me-2"></i> Report a New Issue
                    </a>
                    <a href="/Issue/Track" class="btn btn-primary btn-lg">
                        <i class="bi bi-search me-2"></i> Check Ticket Status
                    </a>
                </div>
            </div>
        </div>
    </div>

    <footer class="text-center mt-5 small">
        © 2025 Namchow IT. All rights reserved.
    </footer>
</div>

{"GlobalPropertiesHash": "ac3qQLEwNBqSoyXPXvZ9st012XkwKrvimB2p+ozE/ic=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["W/MIe4AVg7KbAcyfWoeyyTQOLm4SrjQDFnaodJhq/AM=", "lCSCZbel2WPWSTCmd5cSYz+wtT6KS9tfxfDdu5HlhOQ=", "8nhfNGntfrOtofzmUYLs7D6FV7ZyLlr/eRDubG5WEb0=", "u1JA125wNDq2F0Ji+Ty9wHYbANDuRxJTJsJjnvcJ0g4=", "6UERzuXHaSfFmzoEubkWKgt4+vjfkF0SKGt1KA1uq9k=", "5DqcBhWMdx+puADzGYRA6SDQbPD0HXaqh7h8OL//plE=", "DFunNdBk6WFrzfdsVR9Q464KzVHL64wMVxGMzFZWN74=", "B213H/HTn//w9hfjaRn/yW2qYFErGJzEg4f+ZaVJ6s8=", "jEGjjVFiHw3DEb3QHz/1LAYS1UKlRUsJyD9nrIDV4CM=", "96+v9e7j+kuEl46KrZmxg5KmI3PX719ZsFVDS2ybCas=", "Vn5+X1MDgyv9ZiJ+d7V0JR3rEbcm7J5rzWH/hJuQerg=", "j3Z41WlUnSCv5Vr/qdYT7ODoUmh7t1rO3Ec2s20+Kz0=", "CiHgjhW/NLcisjoySzAy729WPhl9m/NTciI5ZDJzGI4=", "+y9fGEj/xHchqf+SRwzw8MbNoL+iQJvbGSMHhp56Z/Q=", "0MCrFnwcv/LOJ5lPVDtg8U7sIAAW92TB4VRNmOX1WQs=", "l0VP4JykQPcV+A2XMC67VE8bquhO2rUORJRdfh/h2mY=", "yl89t6N1ThY1AEq7UhEOnGwJXZ4FQk9RjMMbDpQFgzw=", "7dV+OJwhVS4GXZiaSns5wKngMwMyJSzGADo4ok+eGKQ=", "SZOlrS6wlfQm45LIcCsnS0CPMxsUN6XU9Pb3hBT5tl8=", "mw6dgZzHLeoDsqf92Uu1K2RSqfRWzUkF7QBs0UhLb+A=", "SxBRMWB8qPgE2hiF4baQKXWm0C80RNp4jzw1Nno8xfI=", "8MN2oYIz4HUBqtvieNXeCUDuPW6tfc301iOH6am+1Qk=", "DI4OC2WvGitCW0nsWOEQt9s/qA/T7pwo+21DQFQ1giI=", "c+kSfSzNH1s3Xxa4XxXLV/YeHvQ1BlTOEXC5xRdVjZA=", "say6ioPADay0hgcUHbsP5kfugIaHHZIo04dbbcad5yI=", "mbg1BVL+G+LlufpO4XpuRCj9KrFgH96j2Imizzog9H4=", "2d6Bo0KYWwdcoGyDsnkrS9nDcs27J9vDHnBhje9i8u8=", "wSY5ByMH1lAElBwBxiq5lZeuUDpqmhoHzNslJueB/Mg=", "JjEiJDiWjHjzZ7mEdZW3aWk7iQzjdqJEe9WFrPBe8Xw=", "m4JStQdDkbG57tVpM/eS8etort5fVgHny6ztxnf3/W0=", "1woZS2dcIu7U97oEG9KxhmCrBGgrnUnxNVkQW7eHJlk=", "522IuBiI6sVAyi5iOaoqJu5U7zFtjc05gmydU9YmVM8=", "vPDjCxV6Pq4J15rdOBhKjO6GZorl+IDuRvwBgN8c8e4=", "gVxJKVO6jnSTmo0VBG91jOc3PfhN73k9FWjDM+wXuMU=", "dMQiNCW1EbJBClwd4Wn39UHBzS4k13oYyK7u6nunmbk=", "QsN60Rw5C/gCK1BwEoe+uTDSAsq9GU+bc2maH6m8N6k=", "ZBQVGkBYdPfvmyQt94k6A9MGPZpsZi8Zu7+BBZ88sPM=", "ErKoxwPqlIHL0gGOpZRjfWIQGlUNIBDnAtrzhLXDxZg=", "eCfzIGQuwVTItNVVmbmdUGA7wGUde1XmvQaXf6xDHWE=", "ecOjXzBUvx3eVQkRbt1duQ+V5bu2tYXJKdi3H79yh4Y=", "JNibdf/XpNNkF1h90tzSWdpMRmh9Qc5uYpln4c6A9bA=", "mkHjO503k1RzDPCd/nz4slbbDwnuxiSct5LNd/2N8ag=", "DrCfW3nYH8jUQX9dBtujLfkP5fY/fta+JxqAuhgFRek=", "0dTdfGANbuYHBgmNnj58wrJIVE0Of80ri+UYtGRosW4=", "5c5BNRxgIczv5n/km8K09o7DxWrSs9+cWevA6Blv7fs=", "h07Hy+SB30hN5vFqZ9//WCOmeiopwPwid6STH7tx66A=", "rbxbjG7Ua6T2zwU55AgPVhXknLjpIHhMFJYWfQ8ndRI=", "Kv3O7xvP0MRMzsgGQYvr++3qGBf0VnhR/ij+iP2EqSM=", "mJQ+LVoBl/XI4OgXlQvoFm5OejQy3MQcCN+jPpIwJpM=", "3whcPuagE0tn3x1KdZYwiDucPhxgrFWu0u8o3dsgPVI=", "p3elLzMV5E11nAdmUtxqmC8A5w30S+BwXpkJT05Lnu8=", "w23e+zIdhGp9ts7NKOli1BKncmlJhY4Wox2O/gRpG3I=", "35zKs1lo43QjkV9sfS4y5cSbmJkDwkszwKbVmPAs6D4=", "t1ztC3g4CFfJGG8u6d18PU8qJOE5ecB6JW4wTL8t4tk=", "+0PYgrMsvMTMQ1y0EJLxbxD73OagL1T6fqwY0AUDY0Y=", "AC65g+oQHRFipqe+ZzL44oXtb7bzO0fhHUouaCJ7+XQ=", "ilB/d5xQQnIf2AEQhQiIvBRHWec4wBiJSq+CdLFZO6g=", "l67D9XMK2ZiCD74dnaNizBqBx4N4cuJfrXu3ZbAr8r8=", "syDFSju9z8QXrgFU5qqQ6JMrz/1Idj8MJ00og+9U4g8=", "rpUPwIDIxROOZ/g+z8tQV+HoZGvoYmYspNDJZIEL2QU=", "KA98sq/hlD8jEGE3CO45VfinhJ18MGlViKLeUhW4Stc=", "ULe1A5OsxeXZ9xFlp3H5QUWWpYUrfzZWEV6httIaFeU=", "tuPf/vlNGhJVsT4G9tq8T1zAI+PSmotLkaE7WXbKsB8=", "BhqFblGj6iIaoAhIibeSWV3EpZEMM0UxE/E90WSBoHI=", "a1zsm+rophIHd8luC+WVfZKxe6/1OWlXFlXYLy8DDtk=", "6k8CWU/IShZICBCWHGrIbQyBkzALbR9Q1r/wbYk8Ohs=", "FtYGk2sCZm+Y26QYyuzs5is2q91vZONkr7buUaBYwKY=", "aAIExh5OCt9R77QtBN7L/jl60GyY76Q1VukRz6iy0nY=", "L0eG91kwTpjovbEaSMCNhu5O07aEQqMhF+N+mbD1Jqs=", "PEKWSSTg2FJux0elVU+2BurYMKQurtPsNExJp9QvT2U=", "P4LqQOqQQkiRJsX9LgV1krdX3T3bMfgws9IklHiZ1FQ=", "Zk3yE1efNAPwKt7pLbXoy3GNV8UiVt2pp+nVnQa87Ck=", "smm+RgGb+hNlhMY9iZeJBSbYeIp7LrMyG8/XOop2yPk=", "0gmrdQAvePGqhurR7QrfDVMjwRcup42/FSE1v9eVGIE=", "wefLSlfwjsCqU7E1b7S0xQwKZw6hWj2nM5Y5UoIuYqQ="], "CachedAssets": {"W/MIe4AVg7KbAcyfWoeyyTQOLm4SrjQDFnaodJhq/AM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\css\\site.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-01T03:19:16.0027816+00:00"}, "lCSCZbel2WPWSTCmd5cSYz+wtT6KS9tfxfDdu5HlhOQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\favicon.ico", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-01T03:19:16.1625019+00:00"}, "8nhfNGntfrOtofzmUYLs7D6FV7ZyLlr/eRDubG5WEb0=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\js\\site.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-01T03:19:16.0087819+00:00"}, "u1JA125wNDq2F0Ji+Ty9wHYbANDuRxJTJsJjnvcJ0g4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-01T03:19:15.4570781+00:00"}, "6UERzuXHaSfFmzoEubkWKgt4+vjfkF0SKGt1KA1uq9k=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-01T03:19:15.469071+00:00"}, "5DqcBhWMdx+puADzGYRA6SDQbPD0HXaqh7h8OL//plE=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-01T03:19:15.4890792+00:00"}, "DFunNdBk6WFrzfdsVR9Q464KzVHL64wMVxGMzFZWN74=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-01T03:19:15.4950716+00:00"}, "B213H/HTn//w9hfjaRn/yW2qYFErGJzEg4f+ZaVJ6s8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-01T03:19:15.5090716+00:00"}, "jEGjjVFiHw3DEb3QHz/1LAYS1UKlRUsJyD9nrIDV4CM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-01T03:19:15.5350744+00:00"}, "96+v9e7j+kuEl46KrZmxg5KmI3PX719ZsFVDS2ybCas=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-01T03:19:15.5905795+00:00"}, "Vn5+X1MDgyv9ZiJ+d7V0JR3rEbcm7J5rzWH/hJuQerg=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-01T03:19:15.6241546+00:00"}, "j3Z41WlUnSCv5Vr/qdYT7ODoUmh7t1rO3Ec2s20+Kz0=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-01T03:19:15.6334613+00:00"}, "CiHgjhW/NLcisjoySzAy729WPhl9m/NTciI5ZDJzGI4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-01T03:19:15.6374687+00:00"}, "+y9fGEj/xHchqf+SRwzw8MbNoL+iQJvbGSMHhp56Z/Q=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-01T03:19:15.6470587+00:00"}, "0MCrFnwcv/LOJ5lPVDtg8U7sIAAW92TB4VRNmOX1WQs=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-01T03:19:15.651058+00:00"}, "l0VP4JykQPcV+A2XMC67VE8bquhO2rUORJRdfh/h2mY=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-01T03:19:15.6570583+00:00"}, "yl89t6N1ThY1AEq7UhEOnGwJXZ4FQk9RjMMbDpQFgzw=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-01T03:19:15.6600592+00:00"}, "7dV+OJwhVS4GXZiaSns5wKngMwMyJSzGADo4ok+eGKQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-01T03:19:15.6717372+00:00"}, "SZOlrS6wlfQm45LIcCsnS0CPMxsUN6XU9Pb3hBT5tl8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-01T03:19:15.6757368+00:00"}, "mw6dgZzHLeoDsqf92Uu1K2RSqfRWzUkF7QBs0UhLb+A=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-01T03:19:15.6822434+00:00"}, "SxBRMWB8qPgE2hiF4baQKXWm0C80RNp4jzw1Nno8xfI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-01T03:19:15.6883146+00:00"}, "8MN2oYIz4HUBqtvieNXeCUDuPW6tfc301iOH6am+1Qk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-01T03:19:15.7043258+00:00"}, "DI4OC2WvGitCW0nsWOEQt9s/qA/T7pwo+21DQFQ1giI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-01T03:19:15.7082975+00:00"}, "c+kSfSzNH1s3Xxa4XxXLV/YeHvQ1BlTOEXC5xRdVjZA=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-01T03:19:15.7174141+00:00"}, "say6ioPADay0hgcUHbsP5kfugIaHHZIo04dbbcad5yI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-01T03:19:15.7234176+00:00"}, "mbg1BVL+G+LlufpO4XpuRCj9KrFgH96j2Imizzog9H4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-01T03:19:15.7386482+00:00"}, "2d6Bo0KYWwdcoGyDsnkrS9nDcs27J9vDHnBhje9i8u8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-01T03:19:15.7436487+00:00"}, "wSY5ByMH1lAElBwBxiq5lZeuUDpqmhoHzNslJueB/Mg=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-01T03:19:15.7526475+00:00"}, "JjEiJDiWjHjzZ7mEdZW3aWk7iQzjdqJEe9WFrPBe8Xw=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-01T03:19:15.7625068+00:00"}, "m4JStQdDkbG57tVpM/eS8etort5fVgHny6ztxnf3/W0=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-01T03:19:15.7745234+00:00"}, "1woZS2dcIu7U97oEG9KxhmCrBGgrnUnxNVkQW7eHJlk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-01T03:19:15.7815231+00:00"}, "522IuBiI6sVAyi5iOaoqJu5U7zFtjc05gmydU9YmVM8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-01T03:19:15.8139007+00:00"}, "vPDjCxV6Pq4J15rdOBhKjO6GZorl+IDuRvwBgN8c8e4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-01T03:19:15.8239019+00:00"}, "gVxJKVO6jnSTmo0VBG91jOc3PfhN73k9FWjDM+wXuMU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-01T03:19:15.8388559+00:00"}, "dMQiNCW1EbJBClwd4Wn39UHBzS4k13oYyK7u6nunmbk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-01T03:19:15.8467691+00:00"}, "QsN60Rw5C/gCK1BwEoe+uTDSAsq9GU+bc2maH6m8N6k=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-01T03:19:15.8662962+00:00"}, "ZBQVGkBYdPfvmyQt94k6A9MGPZpsZi8Zu7+BBZ88sPM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-01T03:19:15.8732937+00:00"}, "ErKoxwPqlIHL0gGOpZRjfWIQGlUNIBDnAtrzhLXDxZg=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-01T03:19:15.8814522+00:00"}, "eCfzIGQuwVTItNVVmbmdUGA7wGUde1XmvQaXf6xDHWE=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-01T03:19:15.8890423+00:00"}, "ecOjXzBUvx3eVQkRbt1duQ+V5bu2tYXJKdi3H79yh4Y=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-01T03:19:15.903029+00:00"}, "JNibdf/XpNNkF1h90tzSWdpMRmh9Qc5uYpln4c6A9bA=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-01T03:19:15.9107467+00:00"}, "mkHjO503k1RzDPCd/nz4slbbDwnuxiSct5LNd/2N8ag=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-01T03:19:15.9207485+00:00"}, "DrCfW3nYH8jUQX9dBtujLfkP5fY/fta+JxqAuhgFRek=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-01T03:19:15.9270809+00:00"}, "0dTdfGANbuYHBgmNnj58wrJIVE0Of80ri+UYtGRosW4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-01T03:19:15.9370732+00:00"}, "5c5BNRxgIczv5n/km8K09o7DxWrSs9+cWevA6Blv7fs=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-01T03:19:15.9453049+00:00"}, "h07Hy+SB30hN5vFqZ9//WCOmeiopwPwid6STH7tx66A=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-01T03:19:15.9575414+00:00"}, "rbxbjG7Ua6T2zwU55AgPVhXknLjpIHhMFJYWfQ8ndRI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-01T03:19:15.9645396+00:00"}, "Kv3O7xvP0MRMzsgGQYvr++3qGBf0VnhR/ij+iP2EqSM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-01T03:19:15.9757019+00:00"}, "mJQ+LVoBl/XI4OgXlQvoFm5OejQy3MQcCN+jPpIwJpM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-01T03:19:16.1724472+00:00"}, "3whcPuagE0tn3x1KdZYwiDucPhxgrFWu0u8o3dsgPVI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-01T03:19:16.1774383+00:00"}, "p3elLzMV5E11nAdmUtxqmC8A5w30S+BwXpkJT05Lnu8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-01T03:19:15.9857128+00:00"}, "w23e+zIdhGp9ts7NKOli1BKncmlJhY4Wox2O/gRpG3I=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-01T03:19:16.1233911+00:00"}, "35zKs1lo43QjkV9sfS4y5cSbmJkDwkszwKbVmPAs6D4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-01T03:19:16.1363904+00:00"}, "t1ztC3g4CFfJGG8u6d18PU8qJOE5ecB6JW4wTL8t4tk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-01T03:19:16.1543939+00:00"}, "+0PYgrMsvMTMQ1y0EJLxbxD73OagL1T6fqwY0AUDY0Y=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-01T03:19:16.1655088+00:00"}, "AC65g+oQHRFipqe+ZzL44oXtb7bzO0fhHUouaCJ7+XQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-01T03:19:15.9837158+00:00"}, "ilB/d5xQQnIf2AEQhQiIvBRHWec4wBiJSq+CdLFZO6g=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-01T03:19:16.0127847+00:00"}, "l67D9XMK2ZiCD74dnaNizBqBx4N4cuJfrXu3ZbAr8r8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-01T03:19:16.0319207+00:00"}, "syDFSju9z8QXrgFU5qqQ6JMrz/1Idj8MJ00og+9U4g8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-01T03:19:16.0473629+00:00"}, "rpUPwIDIxROOZ/g+z8tQV+HoZGvoYmYspNDJZIEL2QU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-01T03:19:16.0746489+00:00"}, "KA98sq/hlD8jEGE3CO45VfinhJ18MGlViKLeUhW4Stc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-01T03:19:16.0921021+00:00"}, "ULe1A5OsxeXZ9xFlp3H5QUWWpYUrfzZWEV6httIaFeU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-01T03:19:16.1083921+00:00"}, "tuPf/vlNGhJVsT4G9tq8T1zAI+PSmotLkaE7WXbKsB8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-01T03:19:15.9807136+00:00"}}, "CachedCopyCandidates": {}}
@{
    ViewData["Title"] = "iTicket System";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            margin: 0;
            position: relative;
            overflow-x: hidden;
        }

        .container {
            position: relative;
            z-index: 2;
        }

        /* Header */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #2c3e50 !important;
            display: flex;
            align-items: center;
        }



        .navbar-nav .nav-link {
            color: #2c3e50 !important;
            font-weight: 500;
            margin: 0 1rem;
            transition: color 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: #27ae60 !important;
        }

        /* Main content */
        .main-content {
            padding: 2rem 0;
            max-width: 1200px;
            margin: 0 auto;
        }

        .hero-section {
            text-align: left;
            margin-bottom: 3rem;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #2c3e50;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: #5a6c7d;
            margin-bottom: 3rem;
        }

        .service-section h2 {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        .button-container {
            display: flex !important;
            gap: 1rem !important;
            flex-wrap: wrap !important;
        }

        /* Buttons */
        .btn-custom {
            padding: 1rem 2rem !important;
            font-size: 1.1rem !important;
            font-weight: 600 !important;
            border-radius: 10px !important;
            text-decoration: none !important;
            display: inline-block !important;
            margin: 0.5rem 1rem 0.5rem 0 !important;
            transition: all 0.3s ease !important;
            border: none !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
            min-width: 220px !important;
            text-align: center !important;
            visibility: visible !important;
        }

        .btn-report {
            background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
            color: white !important;
        }

        .btn-report:hover {
            background: linear-gradient(135deg, #229954, #27ae60) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3) !important;
            color: white !important;
        }

        .btn-check {
            background: linear-gradient(135deg, #3498db, #2980b9) !important;
            color: white !important;
        }

        .btn-check:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79) !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3) !important;
            color: white !important;
        }

        /* Illustration area */
        .illustration-area {
            position: relative;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #ffffff;
            border-radius: 20px;
        }



        /* Footer */
        .footer {
            color: #5a6c7d;
            font-size: 0.9rem;
            margin-top: 3rem;
        }

        /* Responsive */
        @@media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .btn-custom {
                display: block;
                width: 100%;
                margin: 0.5rem 0;
            }

            .illustration-area {
                height: 300px;
                margin-top: 2rem;
            }

            .support-character {
                width: 200px;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->


    <!-- Main Content -->
    <div class="container main-content">
        <div class="row align-items-center justify-content-center min-vh-100">
            <div class="col-lg-6">
                <div class="hero-section">
                    <h1 class="hero-title">iTicket System</h1>
                    <p class="hero-subtitle">Effortless IT issue reporting and tracking</p>

                    <div class="service-section">
                        <h2>Select a service</h2>
                        <div class="button-container">
                            <a href="/iTicket/Create" class="btn-custom btn-report">
                                Report a New Issue
                            </a>
                            <a href="/iTicket/Track" class="btn-custom btn-check">
                                Check Ticket Status
                            </a>
                        </div>
                    </div>
                </div>

                <div class="footer">
                    © 2025 Namchow IT. All rights reserved.
                </div>
            </div>

            <div class="col-lg-6">
                <div class="illustration-area">
                    <img src="/images/IT_Person.png" alt="IT Support Person" class="img-fluid" style="max-height: 350px;">
            </div>
        </div>
    </div>
    

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

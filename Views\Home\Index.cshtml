@{
    ViewData["Title"] = "iTicket System";
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #ffffff;
            min-height: 100vh;
            margin: 0;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .hero-section {
            text-align: left;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            color: #2c3e50;
        }

        .hero-subtitle {
            font-size: 1.2rem;
            color: #5a6c7d;
            margin-bottom: 3rem;
        }

        .service-section h2 {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        .button-container {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: flex-start;
            margin-top: 1rem;
            margin-bottom: 2rem;
        }

        .btn-custom {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 10px;
            text-decoration: none;
            border: none;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            min-width: 220px;
            text-align: center;
            transition: all 0.3s ease;
            color: white;
        }

        .btn-report {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .btn-report:hover {
            background: linear-gradient(135deg, #229954, #27ae60);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
            color: white;
        }

        .btn-check {
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .btn-check:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.3);
            color: white;
        }

        .illustration-area {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 400px;
        }

        .footer {
            color: #5a6c7d;
            font-size: 0.9rem;
            margin-top: 3rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .button-container {
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-top: 1rem;
                margin-bottom: 2rem;
            }

            .btn-custom {
                width: 100%;
                max-width: 300px;
                margin: 0.5rem 0;
            }

            .illustration-area {
                height: 300px;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-lg main-content">
        <div class="row min-vh-100 align-items-center justify-content-center g-5">
            <!-- TEXT SIDE -->
            <div class="col-md-6 col-lg-5 text-center text-md-start">
                <div class="hero-section">
                    <h1 class="hero-title">iTicket System</h1>
                    <p class="hero-subtitle">Effortless IT issue reporting and tracking</p>

                    <div class="service-section">
                        <h2>Select a service</h2>
                        <div class="button-container">
                            <a href="/iTicket/Create" class="btn-custom btn-report">
                                Report a New Issue
                            </a>
                            <a href="/iTicket/Track" class="btn-custom btn-check">
                                Check Ticket Status
                            </a>
                        </div>
                    </div>

                    <div class="footer">
                        © 2025 Namchow IT. All rights reserved.
                    </div>
                </div>
            </div>

            <!-- IMAGE SIDE -->
            <div class="col-md-6 col-lg-5 text-center">
                <div class="illustration-area">
                    <img src="/images/IT_Person.png" alt="IT Support Person" class="img-fluid" style="max-height: 350px;">
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

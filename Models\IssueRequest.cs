public class IssueRequest
{
    public int Id { get; set; }
    public string TicketNo { get; set; }
    public DateTime RequestDate { get; set; } = DateTime.Now;
    public string RequesterName { get; set; }
    public string ContactEmail { get; set; }
    public string ContactPhone { get; set; }
    public string IssueTitle { get; set; }
    public string IssueDetail { get; set; }
    public string AttachmentPath { get; set; }
    public string RequesterIP { get; set; }
    public string UserAgent { get; set; }
    public int? PriorityId { get; set; }
    public int? StatusId { get; set; }
    public string Token { get; set; }
}

{"GlobalPropertiesHash": "ac3qQLEwNBqSoyXPXvZ9st012XkwKrvimB2p+ozE/ic=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["W/MIe4AVg7KbAcyfWoeyyTQOLm4SrjQDFnaodJhq/AM=", "lCSCZbel2WPWSTCmd5cSYz+wtT6KS9tfxfDdu5HlhOQ=", "8nhfNGntfrOtofzmUYLs7D6FV7ZyLlr/eRDubG5WEb0=", "FMVDUf74Cv/KPI+wSMXee3a90cOx1I5aB9KM9hRZfcw=", "fJCP6S7Q0Sxbw4xcBmKtEIWfGuLcx1c01UWD2HSPlYo=", "n79TnWf21X0IPlGwuJ0/L+sI8nuGB9cf/xsVO3J0bm4=", "yiw6aCqH6etJ4Dn3VlgeECaRq9IA44MskT+gaqx9lxs=", "78/WE8u8BDkpIO92VbArNwgCaemNef5hqmzIo3cQ0NI=", "7toCrK1DQuA5l+Ik3hBKXRYBNyWWPJo3rzBZ88PyaPA=", "PLq1lkQ/1XBm+7xK48VEBrdJL9bOWdPtFhqJQLNBY38=", "dpfp08xuHo4X+O8b+AejxeN7Iq7smv7dNzjuNpwI6Cc=", "TDKb+RZ4PWt5LlrvBKpYYtnXVFj0NRuxNvhE9PqviSc=", "eNxGtcb3SIr+xTC8lzXJWL4XwgfqClTcwTFi4BHRQZo=", "oI9gaWhi1XWDYDGd7JSbuku+qauuEQAN94sbmI3G/a8=", "1G4fug2U/uFJs5qCyaast7QwL2sWmEvjJlWMN6vIrhY=", "i4OtHQ5lLgdBzzGjAV48BBFyzhQWSOhkNdX8/r0Z828=", "C8Jd5VLWL0FYtT74D8xuO0lMe5wloYlQavJFHLUWYfw=", "a77yG2CUW87mbsZlBxXuAzW+5j5AZiylXggJrZEe2Hw=", "YZx/FRklupYw3PeqVVaLzmmMiJ+8mDGAsPxUkbqbHJ4=", "S6bm9m1iyowAT9RDox0/hUoCjw14Vo95c4UCAxzaOgM=", "U+FQqWsqLah+laZQBB6vePZqQgaAwGMzOYoHHpqB818=", "doW4g+eCyICyKbewIs1hnoqVyXqvvJwMkujICGfUutM=", "Du16WLa4yxjaGTwinjAfgAr1DQSJYO3za6hzS+Q5j2s=", "qi7N3rmzYCDum9+o7xtRgpykwqSf8eF41unwQSocz7g=", "NtJ22LfOK9hTPT5/1O1LE2iDs8h/dgO2S2Y8jYYMuoc=", "gviqdwwLbBda9OI8V6rs7BcNIUFIaJ6GZhhTQYBtxc8=", "E+Hm7vxHwLodgy08fcLc7zflvpXmehs6vtL0o4OUe4s=", "bc5n5KXGKYKBMKECM/Of4DKaPgviQxhZGWCFa1EryK0=", "cKfeRcr3ndnVe69yDqsS7dqyVpGxsrjgsdW91JaSr9I=", "ttz27tM6DTis6e7ZNo+3TMhEhXBo36epeGTcpSB8M6Y=", "OnU+iIBaP/yOhY4ma9+4gyI1Pw5yJwr+8SXYTLCbZeI=", "7LFZpew8AhKEVYIZwoSFDEcQnzR74Q3nBu5L4IrV+w8=", "kiaa5IeedwK+gYFLemqaLgJ2l3ENWDBm+Sjd3bURBLY=", "vI5efiVE+b6+wkHt1u3b75XR2nYUiw79vvfhAdZJIs8=", "ytHxWYoerbLrZuETbUVsL2GVpu0zzyIDfISRQg+I9t8=", "EvZMQ60Gl56FKb0J+jh2O2Z7MMuJSoezVRZZGbd2eqU=", "oDLc/GcPJ7pDSZeUN6IyzaAljL//LAzqg5z8MBuSa20=", "mO1WcYpEH57bFiFpzDihtjBbhWpYghfMR1XFoPZ2KRc=", "pLH7AViZ70KNswzPG4JKUR1GensoYGFw/MEwgEmynwU=", "vH5BUkSxzWGvODNhipee/ghavCtk4+ROQqWpl+e2VHU=", "Hz41EeXVaOKAQ7rqBRbv3aYaxE54Eiwtf+FslacEUsY=", "Pv2ENF+CCD87pwoMzh/8odykmXBKGwqw+p9nwVbMRpA=", "VlEJcQlpbxLCPYB0BMXdmAns1MVVGMZvHvA7ovyiX04=", "HbQkvRUQ9ekYRGK+ctD/UhPd1Kgke1I+cnYQWPYdc+I=", "jIuSxo1l2ou2JeoGEzGXvT5Uw5SzNL70nzHqy2nIbIc=", "pDxLG7kW+nPDN33yM+e5q8oqja56i0eLC+03YGAOKgQ=", "CZ9ICeJ/1gYTjchLRBQqstHl8aE+0gL8eEX7iBBKROk=", "Kv3O7xvP0MRMzsgGQYvr++3qGBf0VnhR/ij+iP2EqSM=", "mJQ+LVoBl/XI4OgXlQvoFm5OejQy3MQcCN+jPpIwJpM=", "3whcPuagE0tn3x1KdZYwiDucPhxgrFWu0u8o3dsgPVI=", "p3elLzMV5E11nAdmUtxqmC8A5w30S+BwXpkJT05Lnu8=", "w23e+zIdhGp9ts7NKOli1BKncmlJhY4Wox2O/gRpG3I=", "35zKs1lo43QjkV9sfS4y5cSbmJkDwkszwKbVmPAs6D4=", "t1ztC3g4CFfJGG8u6d18PU8qJOE5ecB6JW4wTL8t4tk=", "+0PYgrMsvMTMQ1y0EJLxbxD73OagL1T6fqwY0AUDY0Y=", "AC65g+oQHRFipqe+ZzL44oXtb7bzO0fhHUouaCJ7+XQ=", "ilB/d5xQQnIf2AEQhQiIvBRHWec4wBiJSq+CdLFZO6g=", "l67D9XMK2ZiCD74dnaNizBqBx4N4cuJfrXu3ZbAr8r8=", "syDFSju9z8QXrgFU5qqQ6JMrz/1Idj8MJ00og+9U4g8=", "rpUPwIDIxROOZ/g+z8tQV+HoZGvoYmYspNDJZIEL2QU=", "KA98sq/hlD8jEGE3CO45VfinhJ18MGlViKLeUhW4Stc=", "ULe1A5OsxeXZ9xFlp3H5QUWWpYUrfzZWEV6httIaFeU=", "tuPf/vlNGhJVsT4G9tq8T1zAI+PSmotLkaE7WXbKsB8=", "BhqFblGj6iIaoAhIibeSWV3EpZEMM0UxE/E90WSBoHI=", "a1zsm+rophIHd8luC+WVfZKxe6/1OWlXFlXYLy8DDtk=", "6k8CWU/IShZICBCWHGrIbQyBkzALbR9Q1r/wbYk8Ohs=", "FtYGk2sCZm+Y26QYyuzs5is2q91vZONkr7buUaBYwKY=", "aAIExh5OCt9R77QtBN7L/jl60GyY76Q1VukRz6iy0nY=", "L0eG91kwTpjovbEaSMCNhu5O07aEQqMhF+N+mbD1Jqs=", "PEKWSSTg2FJux0elVU+2BurYMKQurtPsNExJp9QvT2U=", "P4LqQOqQQkiRJsX9LgV1krdX3T3bMfgws9IklHiZ1FQ=", "Zk3yE1efNAPwKt7pLbXoy3GNV8UiVt2pp+nVnQa87Ck=", "smm+RgGb+hNlhMY9iZeJBSbYeIp7LrMyG8/XOop2yPk=", "0gmrdQAvePGqhurR7QrfDVMjwRcup42/FSE1v9eVGIE=", "wefLSlfwjsCqU7E1b7S0xQwKZw6hWj2nM5Y5UoIuYqQ="], "CachedAssets": {"W/MIe4AVg7KbAcyfWoeyyTQOLm4SrjQDFnaodJhq/AM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\css\\site.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-01T03:19:16.0027816+00:00"}, "lCSCZbel2WPWSTCmd5cSYz+wtT6KS9tfxfDdu5HlhOQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\favicon.ico", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-01T03:19:16.1625019+00:00"}, "8nhfNGntfrOtofzmUYLs7D6FV7ZyLlr/eRDubG5WEb0=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\js\\site.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-01T03:19:16.0087819+00:00"}, "CZ9ICeJ/1gYTjchLRBQqstHl8aE+0gL8eEX7iBBKROk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fv6m00jlsj", "Integrity": "SzzRmuPLldNWnyFXAZP64VpT+clUa5bND5fQscONDu8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220691, "LastWriteTime": "2025-07-01T04:18:17.0699453+00:00"}, "pDxLG7kW+nPDN33yM+e5q8oqja56i0eLC+03YGAOKgQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7183nd47gn", "Integrity": "lel57Jim1/CW4I1iEkaHexK/J9h/ZRneeORKiQuNOIg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60775, "LastWriteTime": "2025-07-01T04:18:16.9439323+00:00"}, "jIuSxo1l2ou2JeoGEzGXvT5Uw5SzNL70nzHqy2nIbIc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4fapcppmxt", "Integrity": "AKF2JhIGWaZl3cn18u8a6+ILdrSdz8IM2CXSCZouDbw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 294819, "LastWriteTime": "2025-07-01T04:18:17.0309347+00:00"}, "HbQkvRUQ9ekYRGK+ctD/UhPd1Kgke1I+cnYQWPYdc+I=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mykkvd2hkm", "Integrity": "4iVQR37Qe2Tuh7cjJQdKCANjnoR6UO6/cIu9hmy7hZA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145560, "LastWriteTime": "2025-07-01T04:18:16.8799321+00:00"}, "VlEJcQlpbxLCPYB0BMXdmAns1MVVGMZvHvA7ovyiX04=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "586x66nycw", "Integrity": "O9RzA/mzSQDqsLlfgT3EimsUNdYvrynK+iB19rGyO38=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222398, "LastWriteTime": "2025-07-01T04:18:16.9839337+00:00"}, "Pv2ENF+CCD87pwoMzh/8odykmXBKGwqw+p9nwVbMRpA=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7ifmqzf51n", "Integrity": "/+2UOLrBkJMGzFHiKQxcgGnK1mIA94yryJf56OFeNbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 74055, "LastWriteTime": "2025-07-01T04:18:16.63009+00:00"}, "Hz41EeXVaOKAQ7rqBRbv3aYaxE54Eiwtf+FslacEUsY=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2aps6mjopp", "Integrity": "+nx5p5T+n0g8L/OnO6CG2QIgyGtDPtxKfbBhcNN/yF0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 293657, "LastWriteTime": "2025-07-01T04:18:16.841939+00:00"}, "vH5BUkSxzWGvODNhipee/ghavCtk4+ROQqWpl+e2VHU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9nx659ylgg", "Integrity": "K8fPuP/Isv+p0f05Jbutp7bUudxGtciVRgDRN4aMa4c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135984, "LastWriteTime": "2025-07-01T04:18:16.6640993+00:00"}, "pLH7AViZ70KNswzPG4JKUR1GensoYGFw/MEwgEmynwU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0ia45k0zg6", "Integrity": "KMPy0mgJ1P9QNFXDksX2HvkX3SIeO/iM2dOASfZr3oU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332190, "LastWriteTime": "2025-07-01T04:18:16.7569356+00:00"}, "mO1WcYpEH57bFiFpzDihtjBbhWpYghfMR1XFoPZ2KRc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zyma9w1t4a", "Integrity": "NfRUfZNkERrKSFA0c1a8VmCplPDYtpTYj5lQmKe1R/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80821, "LastWriteTime": "2025-07-01T04:18:16.9119345+00:00"}, "oDLc/GcPJ7pDSZeUN6IyzaAljL//LAzqg5z8MBuSa20=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "yhs0gtq433", "Integrity": "RtS+xc/GamZ1j5irErX3kgDrY2wyvUcCisNyIffxHl8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 427772, "LastWriteTime": "2025-07-01T04:18:16.7969476+00:00"}, "EvZMQ60Gl56FKb0J+jh2O2Z7MMuJSoezVRZZGbd2eqU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3631qojtq1", "Integrity": "tQ9c3dc1t0j9EV2Itwqx1ZK0qjrLayj0+l/lSEgU5ZM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207922, "LastWriteTime": "2025-07-01T04:18:16.7040896+00:00"}, "ytHxWYoerbLrZuETbUVsL2GVpu0zzyIDfISRQg+I9t8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h0ed2e8v31", "Integrity": "MQL775aSq9kNSZA9h1GfrfILFLudaUgLi1xZ87cRkNY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 588895, "LastWriteTime": "2025-07-01T04:18:15.4536494+00:00"}, "vI5efiVE+b6+wkHt1u3b75XR2nYUiw79vvfhAdZJIs8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sdlhrk323x", "Integrity": "gP5J22JsVu+X8rzMEh1Kx+lD89HF3DW4YUWPiU9udUc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232126, "LastWriteTime": "2025-07-01T04:18:15.8170895+00:00"}, "kiaa5IeedwK+gYFLemqaLgJ2l3ENWDBm+Sjd3bURBLY=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dwnyfrorjx", "Integrity": "g1c2E/WGT3eJdfDltNW+2b3JVAyG74bcngJUPz4D068=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 680451, "LastWriteTime": "2025-07-01T04:18:15.5046478+00:00"}, "7LFZpew8AhKEVYIZwoSFDEcQnzR74Q3nBu5L4IrV+w8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ajzo1u5v8g", "Integrity": "Gk1oGfOLBLvjrSivQbGbLbVFDYLqIBwry7+HKKrwq08=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 279417, "LastWriteTime": "2025-07-01T04:18:16.1210901+00:00"}, "OnU+iIBaP/yOhY4ma9+4gyI1Pw5yJwr+8SXYTLCbZeI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jpw2fk0kvm", "Integrity": "6a7kXCMLkOvwgsPvI9SphA3lCCVYilUHoqABNauxLDQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589698, "LastWriteTime": "2025-07-01T04:18:16.0180917+00:00"}, "ttz27tM6DTis6e7ZNo+3TMhEhXBo36epeGTcpSB8M6Y=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7z09619q9j", "Integrity": "zRgmWB5PK4CvTx4FiXsxbHaYRBBjz/rvu97sOC7kzXI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232018, "LastWriteTime": "2025-07-01T04:18:15.2663299+00:00"}, "cKfeRcr3ndnVe69yDqsS7dqyVpGxsrjgsdW91JaSr9I=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j1m9o9pn5t", "Integrity": "KfxofL5yt27NqqYinBxvVVVHMUXkaIwbbjHfRKrtgw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 680591, "LastWriteTime": "2025-07-01T04:18:15.8751027+00:00"}, "bc5n5KXGKYKBMKECM/Of4DKaPgviQxhZGWCFa1EryK0=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "t01kyuglfx", "Integrity": "0+OrIbuBPR3HGBpows26fnWeZDEmkNIQOA3m+LUDsuc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 280202, "LastWriteTime": "2025-07-01T04:18:15.6586464+00:00"}, "E+Hm7vxHwLodgy08fcLc7zflvpXmehs6vtL0o4OUe4s=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1vu6lxsd9m", "Integrity": "vTp+wSodqx0KTTRGFPS+5vJ4BeD15YsTyg2jOMvRV8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180472, "LastWriteTime": "2025-07-01T04:18:16.0840892+00:00"}, "gviqdwwLbBda9OI8V6rs7BcNIUFIaJ6GZhhTQYBtxc8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gyzl34zark", "Integrity": "uDMgsoOuJTmS+BX8zHV6BRYFknx6qQ0Iwalq0l/Wnwc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85386, "LastWriteTime": "2025-07-01T04:18:16.4330906+00:00"}, "NtJ22LfOK9hTPT5/1O1LE2iDs8h/dgO2S2Y8jYYMuoc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lp6b53yply", "Integrity": "R5iWDcOIUk3NTNBsL3kFRpmjmwI420+ragzY3hjSxLw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267924, "LastWriteTime": "2025-07-01T04:18:16.1810943+00:00"}, "qi7N3rmzYCDum9+o7xtRgpykwqSf8eF41unwQSocz7g=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jwbupvf1cn", "Integrity": "PaOXwBVjo0LFfThqwemMb2vZPdpJyTJr2wUpY2rlPgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107806, "LastWriteTime": "2025-07-01T04:18:15.3316475+00:00"}, "Du16WLa4yxjaGTwinjAfgAr1DQSJYO3za6hzS+Q5j2s=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8b3e4oeqwp", "Integrity": "vLNGubJ0CJn+dNm5szlXMnn/fvNPyVO6okJ6po7bypU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180636, "LastWriteTime": "2025-07-01T04:18:15.6956474+00:00"}, "doW4g+eCyICyKbewIs1hnoqVyXqvvJwMkujICGfUutM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "sumeqeat2b", "Integrity": "zGK05vwcKWLkLZy/2uHeZiDb8n4lNbBhnxIBWgCGlho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85457, "LastWriteTime": "2025-07-01T04:18:16.305094+00:00"}, "U+FQqWsqLah+laZQBB6vePZqQgaAwGMzOYoHHpqB818=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kpys072oee", "Integrity": "SMU2lB3w0v+EjU5hfM1Hzq4IexxecRDfxm7d+1FMy60=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267983, "LastWriteTime": "2025-07-01T04:18:16.3990906+00:00"}, "S6bm9m1iyowAT9RDox0/hUoCjw14Vo95c4UCAxzaOgM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r71srw9373", "Integrity": "J8DRiUSeWCoJ5KZv3oLznNnguNXxlT91gRfqx4OhLVM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107938, "LastWriteTime": "2025-07-01T04:18:15.5936442+00:00"}, "YZx/FRklupYw3PeqVVaLzmmMiJ+8mDGAsPxUkbqbHJ4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "caz9dtw6qw", "Integrity": "5FsqQmq4FvVRt/DD5i2bg95Y78Bvw57H1qvmo6RwOYw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63942, "LastWriteTime": "2025-07-01T04:18:15.6236497+00:00"}, "a77yG2CUW87mbsZlBxXuAzW+5j5AZiylXggJrZEe2Hw=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9j04k7ff8o", "Integrity": "S2SiHKfwy3m9UatggjsRctCHEnrAMyi5TJfIz4rOtM4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-01T04:18:16.4590929+00:00"}, "C8Jd5VLWL0FYtT74D8xuO0lMe5wloYlQavJFHLUWYfw=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wexc4ua4wj", "Integrity": "2G/FEq/GAS/FZZWBIoL6LXPKps6BtxGXCsTUVrxhozs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129580, "LastWriteTime": "2025-07-01T04:18:16.2441012+00:00"}, "i4OtHQ5lLgdBzzGjAV48BBFyzhQWSOhkNdX8/r0Z828=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "55zgwnarf3", "Integrity": "6VG3XVVVKowjrEBJbrhT393Yicq2mWYUQnROOXzjOJc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-01T04:18:15.5306484+00:00"}, "1G4fug2U/uFJs5qCyaast7QwL2sWmEvjJlWMN6vIrhY=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vn0zb8aorj", "Integrity": "G5m7S/1IY191YwC+jWUJ5M9qJy+K9/aeBvkL7BZfHEw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51368, "LastWriteTime": "2025-07-01T04:18:15.5606503+00:00"}, "oI9gaWhi1XWDYDGd7JSbuku+qauuEQAN94sbmI3G/a8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z4ulzpprs1", "Integrity": "LR3cTZvDtka+FVVZFNhKksjFFc9s5eeQJtL7d+kM8Cg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-01T04:18:16.0450906+00:00"}, "eNxGtcb3SIr+xTC8lzXJWL4XwgfqClTcwTFi4BHRQZo=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2uydcq7in", "Integrity": "vQnPy/uX1PUe524N9eLyJT6Z++kPr1FwjMLB9qcThYQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129565, "LastWriteTime": "2025-07-01T04:18:16.5910975+00:00"}, "TDKb+RZ4PWt5LlrvBKpYYtnXVFj0NRuxNvhE9PqviSc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m4zwc30cs6", "Integrity": "Kg4miiK9t9v3vyB/LZUmz9a92vBlZxc1rZZfKyDO0wQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-01T04:18:16.274089+00:00"}, "dpfp08xuHo4X+O8b+AejxeN7Iq7smv7dNzjuNpwI6Cc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xofyfpxpoh", "Integrity": "8UZkqzSRw4UnhM40O7g3c1TI83av6RGCum1bdZ9q7l8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 115989, "LastWriteTime": "2025-07-01T04:18:15.370647+00:00"}, "PLq1lkQ/1XBm+7xK48VEBrdJL9bOWdPtFhqJQLNBY38=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ds340y6aci", "Integrity": "02smAS4nas/okRY9J2IiVqi15Zdws9MuuybRJtf/xQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51864, "LastWriteTime": "2025-07-01T04:18:15.2985402+00:00"}, "7toCrK1DQuA5l+Ik3hBKXRYBNyWWPJo3rzBZ88PyaPA=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q1l85kr<PERSON>z", "Integrity": "joHEUNOjjpxtq/QNYHOT4JpnFo4439IskmY+bjcTihk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203344, "LastWriteTime": "2025-07-01T04:18:15.9430895+00:00"}, "78/WE8u8BDkpIO92VbArNwgCaemNef5hqmzIo3cQ0NI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zpf6r7iu2p", "Integrity": "F6wTAruMGkJF9VRzPNq55rxR7N4yhnUKcQhxjmrsGRg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70397, "LastWriteTime": "2025-07-01T04:18:16.5400909+00:00"}, "yiw6aCqH6etJ4Dn3VlgeECaRq9IA44MskT+gaqx9lxs=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dojtn27aph", "Integrity": "I0izs3TQUfbPyb/bNRPJ7iVJ0ULOZFtG/nyETTAU/Yc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115912, "LastWriteTime": "2025-07-01T04:18:16.5010927+00:00"}, "n79TnWf21X0IPlGwuJ0/L+sI8nuGB9cf/xsVO3J0bm4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a6wz4zl1d0", "Integrity": "og1MNJxsupki6LpVXZo+rC95f4QLF16m1YUpHn8vtyM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51789, "LastWriteTime": "2025-07-01T04:18:15.7850988+00:00"}, "fJCP6S7Q0Sxbw4xcBmKtEIWfGuLcx1c01UWD2HSPlYo=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4weum1d8i3", "Integrity": "HA9YLrNkqlffVSkFCLsPOLpzvER9oRo1Zkrvt9F9q98=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203340, "LastWriteTime": "2025-07-01T04:18:15.7560886+00:00"}, "FMVDUf74Cv/KPI+wSMXee3a90cOx1I5aB9KM9hRZfcw=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nclwyi8366", "Integrity": "6aHQppfEko/srtbO3ubCBn9bCz25XtsZoZB8XwnYz34=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70323, "LastWriteTime": "2025-07-01T04:18:16.3430931+00:00"}, "Kv3O7xvP0MRMzsgGQYvr++3qGBf0VnhR/ij+iP2EqSM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-01T03:19:15.9757019+00:00"}, "mJQ+LVoBl/XI4OgXlQvoFm5OejQy3MQcCN+jPpIwJpM=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-01T03:19:16.1724472+00:00"}, "3whcPuagE0tn3x1KdZYwiDucPhxgrFWu0u8o3dsgPVI=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-01T03:19:16.1774383+00:00"}, "p3elLzMV5E11nAdmUtxqmC8A5w30S+BwXpkJT05Lnu8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-01T03:19:15.9857128+00:00"}, "w23e+zIdhGp9ts7NKOli1BKncmlJhY4Wox2O/gRpG3I=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-01T03:19:16.1233911+00:00"}, "35zKs1lo43QjkV9sfS4y5cSbmJkDwkszwKbVmPAs6D4=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-01T03:19:16.1363904+00:00"}, "t1ztC3g4CFfJGG8u6d18PU8qJOE5ecB6JW4wTL8t4tk=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-01T03:19:16.1543939+00:00"}, "+0PYgrMsvMTMQ1y0EJLxbxD73OagL1T6fqwY0AUDY0Y=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-01T03:19:16.1655088+00:00"}, "AC65g+oQHRFipqe+ZzL44oXtb7bzO0fhHUouaCJ7+XQ=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-01T03:19:15.9837158+00:00"}, "ilB/d5xQQnIf2AEQhQiIvBRHWec4wBiJSq+CdLFZO6g=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-01T03:19:16.0127847+00:00"}, "l67D9XMK2ZiCD74dnaNizBqBx4N4cuJfrXu3ZbAr8r8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-01T03:19:16.0319207+00:00"}, "syDFSju9z8QXrgFU5qqQ6JMrz/1Idj8MJ00og+9U4g8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-01T03:19:16.0473629+00:00"}, "rpUPwIDIxROOZ/g+z8tQV+HoZGvoYmYspNDJZIEL2QU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-01T03:19:16.0746489+00:00"}, "KA98sq/hlD8jEGE3CO45VfinhJ18MGlViKLeUhW4Stc=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-01T03:19:16.0921021+00:00"}, "ULe1A5OsxeXZ9xFlp3H5QUWWpYUrfzZWEV6httIaFeU=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-01T03:19:16.1083921+00:00"}, "tuPf/vlNGhJVsT4G9tq8T1zAI+PSmotLkaE7WXbKsB8=": {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-01T03:19:15.9807136+00:00"}}, "CachedCopyCandidates": {}}
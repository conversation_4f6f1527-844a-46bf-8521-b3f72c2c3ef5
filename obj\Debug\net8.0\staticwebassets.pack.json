{"Files": [{"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\iTicketSystem.bundle.scp.css", "PackagePath": "staticwebassets\\iTicketSystem.bundle.scp.css"}, {"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\css\\site.css", "PackagePath": "staticwebassets\\css\\site.css"}, {"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\favicon.ico", "PackagePath": "staticwebassets\\favicon.ico"}, {"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\images\\IT_Person.png", "PackagePath": "staticwebassets\\images\\IT_Person.png"}, {"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\images\\logo_namchow.png", "PackagePath": "staticwebassets\\images\\logo_namchow.png"}, {"Id": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\js\\site.js", "PackagePath": "staticwebassets\\js\\site.js"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.build.iTicketSystem.props", "PackagePath": "build\\iTicketSystem.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildMultiTargeting.iTicketSystem.props", "PackagePath": "buildMultiTargeting\\iTicketSystem.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.buildTransitive.iTicketSystem.props", "PackagePath": "buildTransitive\\iTicketSystem.props"}, {"Id": "obj\\Debug\\net8.0\\staticwebassets\\msbuild.iTicketSystem.Microsoft.AspNetCore.StaticWebAssets.props", "PackagePath": "build\\Microsoft.AspNetCore.StaticWebAssets.props"}], "ElementsToRemove": []}
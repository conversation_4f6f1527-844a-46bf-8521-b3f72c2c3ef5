{"Version": 1, "Hash": "01eVmUj/h02NoocPeDn8zZa5TJA9ceq0DA7ptGLuIRE=", "Source": "iTicketSystem", "BasePath": "_content/iTicketSystem", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "iTicketSystem\\wwwroot", "Source": "iTicketSystem", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\bundle\\iTicketSystem.styles.css", "SourceId": "iTicketSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/iTicketSystem", "RelativePath": "iTicketSystem.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\bundle\\iTicketSystem.styles.css"}, {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\iTicketSystem.bundle.scp.css", "SourceId": "iTicketSystem", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/iTicketSystem", "RelativePath": "iTicketSystem.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\iTicketSystem.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\css\\site.css", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\favicon.ico", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "favicon.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico"}, {"Identity": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\js\\site.js", "SourceId": "iTicketSystem", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\wwwroot\\", "BasePath": "_content/iTicketSystem", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}]}
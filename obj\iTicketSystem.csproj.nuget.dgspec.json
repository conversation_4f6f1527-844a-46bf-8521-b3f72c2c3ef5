{"format": 1, "restore": {"C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\iTicketSystem.csproj": {}}, "projects": {"C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\iTicketSystem.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\iTicketSystem.csproj", "projectName": "iTicketSystem", "projectPath": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\iTicketSystem.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\CodeProject\\iTicketSystem\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"MailKit": {"target": "Package", "version": "[4.13.0, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.411/PortableRuntimeIdentifierGraph.json"}}}}}